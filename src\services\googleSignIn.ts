import { GoogleSignin, statusCodes } from '@react-native-google-signin/google-signin';
import { supabase } from '../utils/supabase';

export class GoogleSignInService {
  static isConfigured = false;

  static configure() {
    if (this.isConfigured) return;

    GoogleSignin.configure({
      iosClientId: '521406618633-qnlvheehuo39v8kojplskov57n52a3sh.apps.googleusercontent.com',
      webClientId: '521406618633-mief1kv08aur34v74dsi2fricdjsfas3.apps.googleusercontent.com',
      offlineAccess: true,
      hostedDomain: '',
      forceCodeForRefreshToken: true,
    });

    this.isConfigured = true;
  }

  static async signIn() {
    try {
      // Configure if not already done
      this.configure();

      // Check if device supports Google Play Services
      await GoogleSignin.hasPlayServices();

      // Get user info from Google
      const userInfo = await GoogleSignin.signIn();
      
      if (!userInfo.idToken) {
        throw new Error('No ID token received from Google');
      }

      // Sign in to Supabase with the Google ID token
      const { data, error } = await supabase.auth.signInWithIdToken({
        provider: 'google',
        token: userInfo.idToken,
      });

      if (error) {
        throw error;
      }

      return {
        user: data.user,
        session: data.session,
        userInfo: userInfo.user,
      };
    } catch (error: any) {
      console.error('Google Sign-in error:', error);
      
      if (error.code === statusCodes.SIGN_IN_CANCELLED) {
        throw new Error('Sign in was cancelled');
      } else if (error.code === statusCodes.IN_PROGRESS) {
        throw new Error('Sign in is already in progress');
      } else if (error.code === statusCodes.PLAY_SERVICES_NOT_AVAILABLE) {
        throw new Error('Google Play Services not available');
      } else {
        throw new Error(error.message || 'Failed to sign in with Google');
      }
    }
  }

  static async signOut() {
    try {
      await GoogleSignin.signOut();
      await supabase.auth.signOut();
    } catch (error) {
      console.error('Google Sign-out error:', error);
    }
  }

  static async getCurrentUser() {
    try {
      const userInfo = await GoogleSignin.getCurrentUser();
      return userInfo;
    } catch (error) {
      return null;
    }
  }

  static async isSignedIn() {
    try {
      return await GoogleSignin.isSignedIn();
    } catch (error) {
      return false;
    }
  }
}
